<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>提示词生成器优化测试</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            min-height: 100vh;
        }
        
        .demo-container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 12px;
            padding: 24px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
        }
        
        .demo-title {
            text-align: center;
            margin-bottom: 30px;
            color: #303133;
        }
        
        .demo-title h1 {
            background: linear-gradient(135deg, #409eff, #67c23a);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            margin: 0;
        }
        
        .demo-section {
            margin-bottom: 40px;
        }
        
        .demo-section h2 {
            color: #606266;
            border-bottom: 2px solid #f0f2f5;
            padding-bottom: 10px;
            margin-bottom: 20px;
        }
        
        .category-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 12px 16px;
            background: #f8f9fa;
            border: 1px solid #e4e7ed;
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.3s ease;
            margin-bottom: 12px;
        }

        .category-header:hover {
            background: #f0f2f5;
            border-color: #409eff;
        }

        .category-title-demo {
            display: flex;
            align-items: center;
            gap: 8px;
            font-size: 14px;
            font-weight: 600;
            color: #606266;
        }

        .active-filter-demo {
            padding: 2px 6px;
            background: #409eff;
            color: white;
            border-radius: 4px;
            font-size: 11px;
        }

        .category-toggle-demo {
            display: flex;
            align-items: center;
        }

        .toggle-icon-demo {
            transition: transform 0.3s ease;
            color: #909399;
            font-size: 12px;
        }

        .toggle-icon-demo.expanded {
            transform: rotate(180deg);
        }

        .category-cards {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 12px;
            margin-bottom: 20px;
        }
        
        .category-card {
            display: flex;
            align-items: center;
            padding: 16px;
            border: 1px solid #e4e7ed;
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.3s ease;
            background: white;
        }
        
        .category-card:hover {
            border-color: #409eff;
            background: #f0f9ff;
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(64, 158, 255, 0.2);
        }
        
        .category-card.active {
            border-color: #409eff;
            background: linear-gradient(135deg, #409eff, #67c23a);
            color: white;
            box-shadow: 0 4px 12px rgba(64, 158, 255, 0.3);
        }
        
        .category-icon {
            margin-right: 12px;
            font-size: 20px;
            width: 24px;
            text-align: center;
        }
        
        .category-info {
            flex: 1;
        }
        
        .category-name {
            font-size: 14px;
            font-weight: 600;
            line-height: 1.2;
        }
        
        .category-count {
            font-size: 12px;
            opacity: 0.8;
            margin-top: 4px;
        }
        
        .template-items {
            display: flex;
            flex-direction: column;
            gap: 12px;
        }
        
        .template-item {
            padding: 20px;
            border: 1px solid #e4e7ed;
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.3s ease;
            background: white;
            position: relative;
        }
        
        .template-item:hover {
            border-color: #409eff;
            box-shadow: 0 4px 12px rgba(64, 158, 255, 0.2);
            transform: translateY(-2px);
        }
        
        .template-item.active {
            border-color: #409eff;
            background: linear-gradient(135deg, #f0f9ff, #e6f7ff);
            box-shadow: 0 4px 16px rgba(64, 158, 255, 0.3);
        }
        
        .template-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 12px;
        }
        
        .template-name {
            margin: 0;
            font-size: 16px;
            font-weight: 600;
            color: #303133;
            flex: 1;
        }
        
        .role-tag {
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            background: #409eff;
            color: white;
            margin-left: 12px;
        }
        
        .template-description {
            margin: 0;
            font-size: 14px;
            color: #909399;
            line-height: 1.6;
            margin-bottom: 16px;
        }
        
        .template-actions {
            display: flex;
            gap: 12px;
            opacity: 0;
            transition: opacity 0.3s ease;
        }
        
        .template-item:hover .template-actions {
            opacity: 1;
        }
        
        .action-btn {
            padding: 6px 12px;
            font-size: 12px;
            border-radius: 6px;
            border: 1px solid #e4e7ed;
            background: white;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 4px;
        }
        
        .edit-btn:hover {
            background: #409eff;
            color: white;
            border-color: #409eff;
        }
        
        .delete-btn:hover {
            background: #f56c6c;
            color: white;
            border-color: #f56c6c;
        }
        
        .demo-note {
            background: #f8f9fa;
            border: 1px solid #e4e7ed;
            border-radius: 8px;
            padding: 16px;
            margin-top: 20px;
            color: #606266;
            font-size: 14px;
        }
    </style>
</head>
<body>
    <div class="demo-container">
        <div class="demo-title">
            <h1>提示词生成器UI优化效果展示</h1>
            <p>全新的卡片式设计，更直观的交互体验</p>
        </div>
        
        <div class="demo-section">
            <h2>1. 角色分类折叠设计</h2>
            <div class="category-header" onclick="toggleCategories()">
                <div class="category-title-demo">
                    <span>按角色分类</span>
                    <span class="active-filter-demo">设定师</span>
                </div>
                <div class="category-toggle-demo">
                    <span class="toggle-icon-demo" id="toggleIcon">▼</span>
                </div>
            </div>
            <div class="category-cards" id="categoryCards" style="display: none;">
                <div class="category-card active">
                    <div class="category-icon">📋</div>
                    <div class="category-info">
                        <div class="category-name">全部</div>
                        <div class="category-count">25 个模板</div>
                    </div>
                </div>
                <div class="category-card">
                    <div class="category-icon">⚙️</div>
                    <div class="category-info">
                        <div class="category-name">设定师</div>
                        <div class="category-count">8 个模板</div>
                    </div>
                </div>
                <div class="category-card">
                    <div class="category-icon">🎬</div>
                    <div class="category-info">
                        <div class="category-name">导演</div>
                        <div class="category-count">6 个模板</div>
                    </div>
                </div>
                <div class="category-card">
                    <div class="category-icon">👤</div>
                    <div class="category-info">
                        <div class="category-name">人物塑造师</div>
                        <div class="category-count">5 个模板</div>
                    </div>
                </div>
                <div class="category-card">
                    <div class="category-icon">🎮</div>
                    <div class="category-info">
                        <div class="category-name">游戏设计师</div>
                        <div class="category-count">4 个模板</div>
                    </div>
                </div>
                <div class="category-card">
                    <div class="category-icon">✍️</div>
                    <div class="category-info">
                        <div class="category-name">写手</div>
                        <div class="category-count">2 个模板</div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="demo-section">
            <h2>2. 模板列表卡片设计</h2>
            <div class="template-items">
                <div class="template-item active">
                    <div class="template-header">
                        <h4 class="template-name">世界观背景生成模板</h4>
                        <span class="role-tag">设定师</span>
                    </div>
                    <p class="template-description">
                        用于生成小说世界观背景设定，包括历史、地理、文化、政治体系等要素，为故事创作提供丰富的背景支撑。
                    </p>
                    <div class="template-actions">
                        <button class="action-btn edit-btn">
                            ✏️ 编辑
                        </button>
                        <button class="action-btn delete-btn">
                            🗑️ 删除
                        </button>
                    </div>
                </div>
                
                <div class="template-item">
                    <div class="template-header">
                        <h4 class="template-name">角色性格分析模板</h4>
                        <span class="role-tag">人物塑造师</span>
                    </div>
                    <p class="template-description">
                        深度分析角色的性格特征、行为模式、心理动机，帮助作者塑造立体饱满的人物形象。
                    </p>
                    <div class="template-actions">
                        <button class="action-btn edit-btn">
                            ✏️ 编辑
                        </button>
                        <button class="action-btn delete-btn">
                            🗑️ 删除
                        </button>
                    </div>
                </div>
                
                <div class="template-item">
                    <div class="template-header">
                        <h4 class="template-name">情节转折点设计</h4>
                        <span class="role-tag">导演</span>
                    </div>
                    <p class="template-description">
                        设计故事的关键转折点，包括冲突升级、角色成长、剧情反转等要素，增强故事的戏剧张力。
                    </p>
                    <div class="template-actions">
                        <button class="action-btn edit-btn">
                            ✏️ 编辑
                        </button>
                        <button class="action-btn delete-btn">
                            🗑️ 删除
                        </button>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="demo-note">
            <strong>优化亮点：</strong>
            <ul>
                <li>🎨 <strong>现代化设计</strong>：采用卡片式布局，视觉层次清晰</li>
                <li>🎯 <strong>直观操作</strong>：操作按钮带文字说明，用户体验更友好</li>
                <li>📁 <strong>折叠筛选</strong>：角色分类默认收起，节省页面空间</li>
                <li>🏷️ <strong>状态提示</strong>：显示当前选中的筛选条件</li>
                <li>✨ <strong>动效优化</strong>：悬浮效果和过渡动画，交互更流畅</li>
                <li>📊 <strong>信息丰富</strong>：显示分类统计、角色标签等关键信息</li>
                <li>🎨 <strong>渐变配色</strong>：使用现代渐变色彩，视觉效果更佳</li>
                <li>📱 <strong>响应式设计</strong>：适配不同屏幕尺寸</li>
            </ul>
        </div>
    </div>
    
    <script>
        // 切换分类展开状态
        function toggleCategories() {
            const categoryCards = document.getElementById('categoryCards');
            const toggleIcon = document.getElementById('toggleIcon');

            if (categoryCards.style.display === 'none') {
                categoryCards.style.display = 'grid';
                toggleIcon.classList.add('expanded');
                toggleIcon.textContent = '▲';
            } else {
                categoryCards.style.display = 'none';
                toggleIcon.classList.remove('expanded');
                toggleIcon.textContent = '▼';
            }
        }

        // 添加交互效果
        document.querySelectorAll('.category-card').forEach(card => {
            card.addEventListener('click', function() {
                document.querySelectorAll('.category-card').forEach(c => c.classList.remove('active'));
                this.classList.add('active');
            });
        });

        document.querySelectorAll('.template-item').forEach(item => {
            item.addEventListener('click', function() {
                document.querySelectorAll('.template-item').forEach(i => i.classList.remove('active'));
                this.classList.add('active');
            });
        });
    </script>
</body>
</html>

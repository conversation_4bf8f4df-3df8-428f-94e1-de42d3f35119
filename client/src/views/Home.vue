<template>
  <div class="home-container">
    <!-- 古典横幅区域 -->
    <div class="classical-banner">
      <div class="banner-content">
        <div class="scroll-decoration left-scroll"></div>
        <div class="main-title-area">
          <h1 class="classical-title">
            <span class="title-decoration">✦</span>
            AI小说创作助手
            <span class="title-decoration">✦</span>
          </h1>
          <p class="classical-subtitle">挥毫泼墨著华章，智慧助力创佳作</p>
          <div class="ink-wash"></div>
        </div>
        <div class="scroll-decoration right-scroll"></div>
      </div>
    </div>

    <!-- 快捷操作区域 -->
    <div class="action-section">
      <div class="action-container">
        <div class="action-card" @click="$router.push('/projects')">
          <div class="card-icon">📚</div>
          <h3>项目管理</h3>
          <p>管理您的创作项目</p>
        </div>
        <div class="action-card" @click="$router.push('/prompt-generator')">
          <div class="card-icon">✍️</div>
          <h3>提示词生成</h3>
          <p>智能生成创作提示</p>
        </div>
        <div class="action-card" @click="$router.push('/workflow-templates')">
          <div class="card-icon">🎭</div>
          <h3>创作流程</h3>
          <p>配置创作工作流</p>
        </div>
      </div>
    </div>

    <!-- 功能介绍区域 -->
    <div class="feature-section">
      <div class="section-title">
        <h2>功能特色</h2>
        <div class="title-underline"></div>
      </div>
      <div class="feature-grid">
        <div class="feature-card">
          <div class="feature-icon">🖋️</div>
          <div class="feature-content">
            <h3>智能提示词生成</h3>
            <p>根据简单输入自动生成针对不同角色的完整提示词，如同文思泉涌，助您与AI完美交互。</p>
          </div>
        </div>
        <div class="feature-card">
          <div class="feature-icon">📖</div>
          <div class="feature-content">
            <h3>完善数据管理</h3>
            <p>为不同创作阶段建立完整数据体系，如同藏书楼般井然有序，保存每一份创作灵感。</p>
          </div>
        </div>
        <div class="feature-card">
          <div class="feature-icon">🎨</div>
          <div class="feature-content">
            <h3>系统创作流程</h3>
            <p>从世界观设定到章节创作，全流程智能支持，如同名师指导，步步为营著华章。</p>
          </div>
        </div>
        <div class="feature-card">
          <div class="feature-icon">⚙️</div>
          <div class="feature-content">
            <h3>灵活流程配置</h3>
            <p>可视化设计创作流程，自定义AI生成节点，如同量身定制，实现个性化创作体验。</p>
          </div>
        </div>
      </div>
    </div>

    <!-- 诗词装饰 -->
    <div class="poetry-decoration">
      <div class="poetry-text">
        "文章千古事，得失寸心知"
      </div>
    </div>
  </div>
</template>

<script setup>
// Home component setup
</script>

<style scoped>
/* 全局容器样式 */
.home-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #f5f1eb 0%, #ede4d3 50%, #e8dcc0 100%);
  position: relative;
  overflow-x: hidden;
}

/* 古典横幅样式 */
.classical-banner {
  position: relative;
  padding: 60px 0;
  background: linear-gradient(45deg, #2c1810 0%, #4a2c1a 50%, #2c1810 100%);
  margin: -20px -20px 40px -20px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
}

.classical-banner::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image:
    radial-gradient(circle at 20% 20%, rgba(218, 165, 32, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 80% 80%, rgba(139, 69, 19, 0.1) 0%, transparent 50%);
  pointer-events: none;
}

.banner-content {
  display: flex;
  align-items: center;
  justify-content: center;
  max-width: 1200px;
  margin: 0 auto;
  position: relative;
  z-index: 2;
}

.scroll-decoration {
  width: 80px;
  height: 200px;
  background: linear-gradient(to bottom, #8b4513, #a0522d, #8b4513);
  border-radius: 40px;
  position: relative;
  box-shadow: inset 0 0 20px rgba(0, 0, 0, 0.3);
}

.scroll-decoration::before {
  content: '';
  position: absolute;
  top: 10px;
  left: 10px;
  right: 10px;
  bottom: 10px;
  background: #f5deb3;
  border-radius: 30px;
  box-shadow: inset 0 0 10px rgba(139, 69, 19, 0.3);
}

.main-title-area {
  flex: 1;
  text-align: center;
  padding: 0 60px;
  position: relative;
}

.classical-title {
  font-size: 3.5rem;
  color: #daa520;
  margin: 0 0 20px 0;
  font-weight: 700;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
  letter-spacing: 4px;
  position: relative;
}

.title-decoration {
  color: #cd853f;
  font-size: 2rem;
  margin: 0 20px;
  animation: sparkle 2s ease-in-out infinite alternate;
}

@keyframes sparkle {
  0% { opacity: 0.6; transform: scale(1); }
  100% { opacity: 1; transform: scale(1.1); }
}

.classical-subtitle {
  font-size: 1.4rem;
  color: #f5deb3;
  margin: 0;
  font-style: italic;
  letter-spacing: 2px;
  opacity: 0.9;
}

.ink-wash {
  position: absolute;
  bottom: -20px;
  left: 50%;
  transform: translateX(-50%);
  width: 200px;
  height: 20px;
  background: radial-gradient(ellipse, rgba(218, 165, 32, 0.3) 0%, transparent 70%);
  border-radius: 50%;
}

/* 快捷操作区域 */
.action-section {
  padding: 60px 0;
  position: relative;
}

.action-container {
  display: flex;
  justify-content: center;
  gap: 40px;
  max-width: 1000px;
  margin: 0 auto;
  flex-wrap: wrap;
}

.action-card {
  background: linear-gradient(145deg, #faf7f0, #f0ead6);
  border: 2px solid #daa520;
  border-radius: 20px;
  padding: 40px 30px;
  text-align: center;
  cursor: pointer;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow:
    0 8px 25px rgba(218, 165, 32, 0.15),
    inset 0 1px 0 rgba(255, 255, 255, 0.6);
  position: relative;
  overflow: hidden;
  min-width: 200px;
}

.action-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(218, 165, 32, 0.1), transparent);
  transition: left 0.6s;
}

.action-card:hover::before {
  left: 100%;
}

.action-card:hover {
  transform: translateY(-8px) scale(1.02);
  box-shadow:
    0 15px 40px rgba(218, 165, 32, 0.25),
    inset 0 1px 0 rgba(255, 255, 255, 0.8);
  border-color: #cd853f;
}

.card-icon {
  font-size: 3rem;
  margin-bottom: 20px;
  filter: drop-shadow(2px 2px 4px rgba(0, 0, 0, 0.1));
}

.action-card h3 {
  color: #8b4513;
  font-size: 1.4rem;
  margin: 0 0 15px 0;
  font-weight: 600;
  letter-spacing: 1px;
}

.action-card p {
  color: #a0522d;
  font-size: 1rem;
  margin: 0;
  line-height: 1.6;
}

/* 功能介绍区域 */
.feature-section {
  padding: 80px 0;
  position: relative;
}

.feature-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image:
    radial-gradient(circle at 10% 20%, rgba(218, 165, 32, 0.05) 0%, transparent 50%),
    radial-gradient(circle at 90% 80%, rgba(139, 69, 19, 0.05) 0%, transparent 50%);
  pointer-events: none;
}

.section-title {
  text-align: center;
  margin-bottom: 60px;
  position: relative;
  z-index: 2;
}

.section-title h2 {
  font-size: 2.5rem;
  color: #8b4513;
  margin: 0 0 20px 0;
  font-weight: 600;
  letter-spacing: 3px;
  position: relative;
}

.title-underline {
  width: 120px;
  height: 4px;
  background: linear-gradient(to right, transparent, #daa520, transparent);
  margin: 0 auto;
  border-radius: 2px;
}

.feature-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 40px;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
  position: relative;
  z-index: 2;
}

.feature-card {
  background: linear-gradient(145deg, #fefcf7, #f8f4e6);
  border: 1px solid #e6d7c3;
  border-radius: 16px;
  padding: 40px 30px;
  display: flex;
  align-items: flex-start;
  gap: 25px;
  transition: all 0.4s ease;
  box-shadow:
    0 6px 20px rgba(139, 69, 19, 0.08),
    inset 0 1px 0 rgba(255, 255, 255, 0.7);
  position: relative;
  overflow: hidden;
}

.feature-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: linear-gradient(to right, #daa520, #cd853f, #daa520);
  transform: scaleX(0);
  transition: transform 0.4s ease;
}

.feature-card:hover::before {
  transform: scaleX(1);
}

.feature-card:hover {
  transform: translateY(-5px);
  box-shadow:
    0 12px 30px rgba(139, 69, 19, 0.15),
    inset 0 1px 0 rgba(255, 255, 255, 0.9);
  border-color: #daa520;
}

.feature-icon {
  font-size: 3rem;
  flex-shrink: 0;
  filter: drop-shadow(2px 2px 4px rgba(0, 0, 0, 0.1));
  transition: transform 0.3s ease;
}

.feature-card:hover .feature-icon {
  transform: scale(1.1) rotate(5deg);
}

.feature-content h3 {
  color: #8b4513;
  font-size: 1.3rem;
  margin: 0 0 15px 0;
  font-weight: 600;
  letter-spacing: 1px;
}

.feature-content p {
  color: #a0522d;
  font-size: 1rem;
  line-height: 1.7;
  margin: 0;
}

/* 诗词装饰 */
.poetry-decoration {
  text-align: center;
  padding: 60px 0;
  position: relative;
}

.poetry-decoration::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 300px;
  height: 100px;
  background: radial-gradient(ellipse, rgba(218, 165, 32, 0.1) 0%, transparent 70%);
  border-radius: 50%;
}

.poetry-text {
  font-size: 1.6rem;
  color: #8b4513;
  font-style: italic;
  letter-spacing: 3px;
  position: relative;
  z-index: 2;
  opacity: 0.8;
  font-weight: 300;
}

.poetry-text::before,
.poetry-text::after {
  content: '❝';
  font-size: 2rem;
  color: #daa520;
  position: absolute;
  top: -10px;
}

.poetry-text::before {
  left: -40px;
}

.poetry-text::after {
  content: '❞';
  right: -40px;
  top: 10px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .classical-title {
    font-size: 2.5rem;
    letter-spacing: 2px;
  }

  .classical-subtitle {
    font-size: 1.1rem;
    letter-spacing: 1px;
  }

  .action-container {
    gap: 20px;
    padding: 0 20px;
  }

  .action-card {
    min-width: 160px;
    padding: 30px 20px;
  }

  .feature-grid {
    grid-template-columns: 1fr;
    gap: 30px;
  }

  .feature-card {
    flex-direction: column;
    text-align: center;
    gap: 20px;
  }

  .poetry-text {
    font-size: 1.2rem;
    letter-spacing: 2px;
  }

  .poetry-text::before,
  .poetry-text::after {
    display: none;
  }
}

@media (max-width: 480px) {
  .banner-content {
    flex-direction: column;
    gap: 30px;
  }

  .scroll-decoration {
    width: 60px;
    height: 120px;
  }

  .main-title-area {
    padding: 0 20px;
  }

  .classical-title {
    font-size: 2rem;
  }
}
</style>

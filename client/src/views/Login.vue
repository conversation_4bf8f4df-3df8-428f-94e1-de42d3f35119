<template>
  <div class="login-container">
    <div class="login-background">
      <div class="scroll-decoration left-scroll"></div>
      <div class="scroll-decoration right-scroll"></div>
    </div>

    <el-card class="login-card">
      <template #header>
        <div class="card-header">
          <div class="login-title">
            <span class="title-decoration">📚</span>
            <h2>墨韵登录</h2>
            <span class="title-decoration">📚</span>
          </div>
          <p class="login-subtitle">进入您的创作世界</p>
        </div>
      </template>

      <el-form
        ref="loginForm"
        :model="loginData"
        :rules="rules"
        label-width="80px"
        @submit.prevent="handleLogin"
      >
        <el-form-item label="用户名" prop="username">
          <el-input v-model="loginData.username" placeholder="请输入用户名"></el-input>
        </el-form-item>

        <el-form-item label="密码" prop="password">
          <el-input
            v-model="loginData.password"
            type="password"
            placeholder="请输入密码"
            show-password
          ></el-input>
        </el-form-item>

        <el-form-item>
          <el-button type="primary" native-type="submit" :loading="loading">登录</el-button>
        </el-form-item>
      </el-form>
    </el-card>
  </div>
</template>

<script setup>
import { ref, reactive } from 'vue';
import { useRouter } from 'vue-router';
import { ElMessage } from 'element-plus';
import authService from '../services/auth';

const router = useRouter();
const loginForm = ref(null);
const loading = ref(false);

const loginData = reactive({
  username: 'admin',
  password: ''
});

const rules = {
  username: [
    { required: true, message: '请输入用户名', trigger: 'blur' }
  ],
  password: [
    { required: true, message: '请输入密码', trigger: 'blur' }
  ]
};

const handleLogin = async () => {
  if (!loginForm.value) return;

  await loginForm.value.validate(async (valid) => {
    if (valid) {
      loading.value = true;
      try {
        await authService.login(loginData);
        ElMessage.success('登录成功');
        router.push('/');
      } catch (error) {
        console.error('登录失败:', error);
        ElMessage.error(error.response?.data?.message || '登录失败，请检查用户名和密码');
      } finally {
        loading.value = false;
      }
    }
  });
};
</script>

<style scoped>
.login-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 80vh;
}

.login-card {
  width: 400px;
  max-width: 100%;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.login-tips {
  margin-top: 15px;
  color: #909399;
  font-size: 14px;
  text-align: center;
}

.login-tips p {
  margin: 5px 0;
}
</style>

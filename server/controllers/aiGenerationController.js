const { AIConfig, PromptTemplate, Project, WorldSetting, Character, Outline, Chapter, Volume, VolumeChapterGroup, Clue } = require('../models');
const DeepSeekService = require('../services/deepseekService');
const KimiService = require('../services/kimiService');

// 获取用户可用的AI配置
exports.getAvailableAIConfigs = async (req, res) => {
  try {
    const userId = req.user.id;
    
    const configs = await AIConfig.findAll({
      where: { 
        userId, 
        isActive: true 
      },
      attributes: ['id', 'provider', 'model', 'isDefault'],
      order: [['isDefault', 'DESC'], ['createdAt', 'ASC']]
    });

    res.json(configs);
  } catch (error) {
    console.error('获取可用AI配置失败:', error);
    res.status(500).json({ message: '获取可用AI配置失败', error: error.message });
  }
};

// 使用AI生成内容
exports.generateContent = async (req, res) => {
  try {
    const userId = req.user.id;
    const {
      aiConfigId,
      promptTemplateId,
      customPrompt,
      systemPrompt,
      projectId,
      params = {},
      systemParamTypes = {},
      generationOptions = {}
    } = req.body;

    // 验证必填参数
    if (!aiConfigId) {
      return res.status(400).json({ message: 'AI配置ID不能为空' });
    }

    if (!promptTemplateId && !customPrompt) {
      return res.status(400).json({ message: '必须提供提示词模板ID或自定义提示词' });
    }

    // 获取AI配置
    const aiConfig = await AIConfig.findOne({
      where: { id: aiConfigId, userId, isActive: true }
    });

    if (!aiConfig) {
      return res.status(404).json({ message: 'AI配置不存在或已禁用' });
    }

    let finalPrompt = customPrompt;
    let finalSystemPrompt = systemPrompt;

    // 如果使用提示词模板，需要处理模板和参数替换
    if (promptTemplateId) {
      const promptTemplate = await PromptTemplate.findByPk(promptTemplateId);
      if (!promptTemplate) {
        return res.status(404).json({ message: '提示词模板不存在' });
      }

      // 使用与提示词生成器相同的参数替换逻辑
      const templateResult = await generatePromptFromTemplate(
        promptTemplate,
        params,
        systemParamTypes,
        projectId,
        userId
      );

      finalPrompt = templateResult.prompt;

      // 如果模板有系统提示词部分，提取出来
      if (finalPrompt.includes('# AI角色身份卡')) {
        const parts = finalPrompt.split('\n\n---\n\n');
        if (parts.length > 1) {
          finalSystemPrompt = parts[0];
          finalPrompt = parts.slice(1).join('\n\n---\n\n');
        }
      }
    }

    // 支持DeepSeek和Kimi
    if (aiConfig.provider !== 'deepseek' && aiConfig.provider !== 'kimi') {
      return res.status(400).json({ message: `暂不支持 ${aiConfig.provider} 提供商` });
    }

    // 如果baseUrl为空，则使用默认值
    const effectiveBaseUrl = aiConfig.baseUrl && aiConfig.baseUrl.trim() ? aiConfig.baseUrl : undefined;

    let aiService;
    if (aiConfig.provider === 'deepseek') {
      aiService = new DeepSeekService(aiConfig.apiKey, effectiveBaseUrl);
    } else if (aiConfig.provider === 'kimi') {
      aiService = new KimiService(aiConfig.apiKey, effectiveBaseUrl);
    }

    try {
      let generatedContent;

      const options = {
        max_tokens: generationOptions.maxTokens || aiConfig.maxTokens,
        temperature: generationOptions.temperature !== undefined ? generationOptions.temperature : aiConfig.temperature,
        ...aiConfig.config
      };

      if (finalSystemPrompt) {
        generatedContent = await aiService.generateWithSystemPrompt(
          finalSystemPrompt,
          finalPrompt,
          options
        );
      } else {
        generatedContent = await aiService.generateText(finalPrompt, options);
      }

      res.json({
        success: true,
        content: generatedContent,
        usedPrompt: finalPrompt,
        usedSystemPrompt: finalSystemPrompt,
        aiConfig: {
          id: aiConfig.id,
          provider: aiConfig.provider,
          model: aiConfig.model
        }
      });

    } catch (error) {
      console.error('AI生成内容失败:', error);
      res.status(500).json({ 
        success: false,
        message: `AI生成失败: ${error.message}` 
      });
    }

  } catch (error) {
    console.error('生成内容失败:', error);
    res.status(500).json({ message: '生成内容失败', error: error.message });
  }
};

// 为世界观设定生成内容
exports.generateWorldSetting = async (req, res) => {
  try {
    const userId = req.user.id;
    const {
      aiConfigId,
      promptTemplateId,
      projectId,
      category,
      title,
      existingContent = '',
      requirements = '',
      params = {}
    } = req.body;

    // 构建参数数据
    const finalParams = {
      category,
      title,
      existingContent,
      requirements,
      ...params
    };

    // 如果有项目ID，获取项目相关信息
    if (projectId) {
      const project = await Project.findOne({
        where: { id: projectId, userId }
      });

      if (project) {
        finalParams.projectName = project.name;
        finalParams.projectType = project.type;
        finalParams.targetAudience = project.targetAudience;
        finalParams.style = project.style;
      }
    }

    // 调用通用生成接口
    req.body.params = finalParams;
    await exports.generateContent(req, res);

  } catch (error) {
    console.error('生成世界观设定失败:', error);
    res.status(500).json({ message: '生成世界观设定失败', error: error.message });
  }
};

// 流式生成内容
exports.generateContentStream = async (req, res) => {
  try {
    const userId = req.user.id;
    const {
      aiConfigId,
      promptTemplateId,
      customPrompt,
      systemPrompt,
      projectId,
      params = {},
      systemParamTypes = {},
      generationOptions = {}
    } = req.body;

    // 验证必填参数
    if (!aiConfigId) {
      return res.status(400).json({ message: 'AI配置ID不能为空' });
    }

    if (!promptTemplateId && !customPrompt) {
      return res.status(400).json({ message: '必须提供提示词模板ID或自定义提示词' });
    }

    // 获取AI配置
    const aiConfig = await AIConfig.findOne({
      where: { id: aiConfigId, userId, isActive: true }
    });

    if (!aiConfig) {
      return res.status(404).json({ message: 'AI配置不存在或已禁用' });
    }

    let finalPrompt = customPrompt;
    let finalSystemPrompt = systemPrompt;

    // 如果使用提示词模板，需要处理模板和参数替换
    if (promptTemplateId) {
      const promptTemplate = await PromptTemplate.findByPk(promptTemplateId);
      if (!promptTemplate) {
        return res.status(404).json({ message: '提示词模板不存在' });
      }

      // 使用与提示词生成器相同的参数替换逻辑
      const templateResult = await generatePromptFromTemplate(
        promptTemplate,
        params,
        systemParamTypes,
        projectId,
        userId
      );

      finalPrompt = templateResult.prompt;

      // 如果模板有系统提示词部分，提取出来
      if (finalPrompt.includes('# AI角色身份卡')) {
        const parts = finalPrompt.split('\n\n---\n\n');
        if (parts.length > 1) {
          finalSystemPrompt = parts[0];
          finalPrompt = parts.slice(1).join('\n\n---\n\n');
        }
      }
    }

    // 创建AI服务实例
    // 如果baseUrl为空，则使用默认值
    const effectiveBaseUrl = aiConfig.baseUrl && aiConfig.baseUrl.trim() ? aiConfig.baseUrl : undefined;

    let aiService;
    if (aiConfig.provider === 'deepseek') {
      aiService = new DeepSeekService(aiConfig.apiKey, effectiveBaseUrl);
    } else if (aiConfig.provider === 'kimi') {
      aiService = new KimiService(aiConfig.apiKey, effectiveBaseUrl);
    } else {
      return res.status(400).json({ message: `暂不支持 ${aiConfig.provider} 提供商的流式生成` });
    }

    // 设置SSE响应头
    res.writeHead(200, {
      'Content-Type': 'text/event-stream',
      'Cache-Control': 'no-cache',
      'Connection': 'keep-alive',
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Headers': 'Cache-Control'
    });

    // 发送初始化消息
    res.write(`data: ${JSON.stringify({
      type: 'start',
      message: '开始生成内容...',
      usedPrompt: finalPrompt,
      usedSystemPrompt: finalSystemPrompt,
      aiConfig: {
        id: aiConfig.id,
        provider: aiConfig.provider,
        model: aiConfig.model
      }
    })}\n\n`);

    try {
      const options = {
        max_tokens: generationOptions.maxTokens || aiConfig.maxTokens,
        temperature: generationOptions.temperature !== undefined ? generationOptions.temperature : aiConfig.temperature,
        ...aiConfig.config
      };

      // 流式生成回调函数
      const onChunk = (chunk, fullContent) => {
        res.write(`data: ${JSON.stringify({
          type: 'chunk',
          content: chunk,
          fullContent: fullContent
        })}\n\n`);
      };

      let generatedContent;
      if (finalSystemPrompt) {
        generatedContent = await aiService.generateWithSystemPromptStream(
          finalSystemPrompt,
          finalPrompt,
          options,
          onChunk
        );
      } else {
        generatedContent = await aiService.generateTextStream(finalPrompt, options, onChunk);
      }

      // 发送完成消息
      res.write(`data: ${JSON.stringify({
        type: 'complete',
        content: generatedContent,
        success: true
      })}\n\n`);

    } catch (error) {
      console.error('AI流式生成失败:', error);
      res.write(`data: ${JSON.stringify({
        type: 'error',
        message: error.message || 'AI生成失败',
        success: false
      })}\n\n`);
    }

    res.end();

  } catch (error) {
    console.error('流式生成内容失败:', error);
    if (!res.headersSent) {
      res.status(500).json({ message: '流式生成内容失败', error: error.message });
    }
  }
};

// 使用与提示词生成器相同的参数替换逻辑
async function generatePromptFromTemplate(promptTemplate, params, systemParamTypes, projectId, userId) {
  try {
    // 准备替换模板中的变量的数据
    let templateData = {};

    // 如果有项目ID，获取项目信息
    if (projectId) {
      const project = await Project.findOne({ where: { id: projectId, userId } });
      if (project) {
        templateData.project = project.toJSON();
      }
    }

    // 处理模板参数
    let templateParameters = promptTemplate.parameters || [];
    // 确保 templateParameters 是数组
    if (typeof templateParameters === 'string') {
      try {
        templateParameters = JSON.parse(templateParameters);
      } catch (error) {
        console.error('解析模板参数失败:', error);
        templateParameters = [];
      }
    }
    if (!Array.isArray(templateParameters)) {
      templateParameters = [];
    }

    if (templateParameters.length > 0) {
      for (const param of templateParameters) {
        if (!param.key) continue;

        if (param.type === 'system' && param.systemType) {
          // 处理系统参数
          const paramValue = params[param.key];
          if (paramValue) {
            switch (param.systemType) {
              case 'worldSetting':
                if (param.multiple) {
                  const worldSettings = await WorldSetting.findAll({
                    where: { id: paramValue, projectId, userId }
                  });
                  templateData[param.key] = worldSettings.map(ws => {
                    const data = ws.toJSON();
                    delete data.id;
                    delete data.projectId;
                    delete data.userId;
                    delete data.createdAt;
                    delete data.updatedAt;
                    return data;
                  });
                } else {
                  const worldSetting = await WorldSetting.findOne({
                    where: { id: paramValue, projectId, userId }
                  });
                  if (worldSetting) {
                    const data = worldSetting.toJSON();
                    delete data.id;
                    delete data.projectId;
                    delete data.userId;
                    delete data.createdAt;
                    delete data.updatedAt;
                    templateData[param.key] = data;
                  }
                }
                break;

              case 'character':
                if (param.multiple) {
                  const characters = await Character.findAll({
                    where: { id: paramValue, projectId, userId }
                  });
                  templateData[param.key] = characters.map(char => {
                    const data = char.toJSON();
                    delete data.id;
                    delete data.projectId;
                    delete data.userId;
                    delete data.createdAt;
                    delete data.updatedAt;
                    return data;
                  });
                } else {
                  const character = await Character.findOne({
                    where: { id: paramValue, projectId, userId }
                  });
                  if (character) {
                    const data = character.toJSON();
                    delete data.id;
                    delete data.projectId;
                    delete data.userId;
                    delete data.createdAt;
                    delete data.updatedAt;
                    templateData[param.key] = data;
                  }
                }
                break;

              case 'outline':
                if (param.multiple) {
                  const outlines = await Outline.findAll({
                    where: { id: paramValue, projectId, userId }
                  });
                  templateData[param.key] = outlines.map(outline => {
                    const data = outline.toJSON();
                    delete data.id;
                    delete data.projectId;
                    delete data.userId;
                    delete data.createdAt;
                    delete data.updatedAt;
                    return data;
                  });
                } else {
                  const outline = await Outline.findOne({
                    where: { id: paramValue, projectId, userId }
                  });
                  if (outline) {
                    const data = outline.toJSON();
                    delete data.id;
                    delete data.projectId;
                    delete data.userId;
                    delete data.createdAt;
                    delete data.updatedAt;
                    templateData[param.key] = data;
                  }
                }
                break;

              case 'chapter':
                if (param.multiple) {
                  const chapters = await Chapter.findAll({
                    where: { id: paramValue, projectId, userId }
                  });
                  templateData[param.key] = chapters.map(chapter => {
                    const data = chapter.toJSON();
                    delete data.id;
                    delete data.projectId;
                    delete data.userId;
                    delete data.createdAt;
                    delete data.updatedAt;
                    return data;
                  });
                } else {
                  const chapter = await Chapter.findOne({
                    where: { id: paramValue, projectId, userId }
                  });
                  if (chapter) {
                    const data = chapter.toJSON();
                    delete data.id;
                    delete data.projectId;
                    delete data.userId;
                    delete data.createdAt;
                    delete data.updatedAt;
                    templateData[param.key] = data;
                  }
                }
                break;

              case 'volume':
                if (param.multiple) {
                  const volumes = await Volume.findAll({
                    where: { id: paramValue, projectId, userId }
                  });
                  templateData[param.key] = volumes.map(volume => {
                    const data = volume.toJSON();
                    delete data.id;
                    delete data.projectId;
                    delete data.userId;
                    delete data.createdAt;
                    delete data.updatedAt;
                    return data;
                  });
                } else {
                  const volume = await Volume.findOne({
                    where: { id: paramValue, projectId, userId }
                  });
                  if (volume) {
                    const data = volume.toJSON();
                    delete data.id;
                    delete data.projectId;
                    delete data.userId;
                    delete data.createdAt;
                    delete data.updatedAt;
                    templateData[param.key] = data;
                  }
                }
                break;

              case 'clue':
                if (param.multiple) {
                  const clues = await Clue.findAll({
                    where: { id: paramValue, projectId, userId }
                  });
                  templateData[param.key] = clues.map(clue => {
                    const data = clue.toJSON();
                    delete data.id;
                    delete data.projectId;
                    delete data.firstAppearChapterId;
                    delete data.userId;
                    delete data.createdAt;
                    delete data.updatedAt;
                    return data;
                  });
                } else {
                  const clue = await Clue.findOne({
                    where: { id: paramValue, projectId, userId }
                  });
                  if (clue) {
                    const data = clue.toJSON();
                    delete data.id;
                    delete data.projectId;
                    delete data.firstAppearChapterId;
                    delete data.userId;
                    delete data.createdAt;
                    delete data.updatedAt;
                    templateData[param.key] = data;
                  }
                }
                break;
            }
          }
        } else {
          // 对于非系统参数，直接添加到templateData
          templateData[param.key] = params[param.key];
        }
      }

      // 处理未在模板参数中定义的参数
      for (const key in params) {
        if (Array.isArray(templateParameters) && !templateParameters.some(p => p.key === key)) {
          templateData[key] = params[key];
        }
      }
    }

    // 替换模板中的变量
    let generatedPrompt = promptTemplate.template;

    // 增强的模板变量替换逻辑（与提示词生成器相同）
    for (const key in templateData) {
      const value = templateData[key];

      // 处理数组类型的参数（多选系统参数）
      if (Array.isArray(value)) {
        // 替换整个数组的占位符
        const arrayPlaceholder = new RegExp(`\\{\\{${key}\\}\\}`, 'g');
        generatedPrompt = generatedPrompt.replace(arrayPlaceholder, JSON.stringify(value, null, 2));

        // 处理数组索引访问，如 {{paramName[0].property}}
        const arrayIndexRegex = new RegExp(`\\{\\{${key}\\[(\\d+)\\]\\.(\\w+)\\}\\}`, 'g');
        let match;

        // 创建一个临时字符串用于替换
        let tempPrompt = generatedPrompt;

        // 查找所有数组索引访问模式
        while ((match = arrayIndexRegex.exec(generatedPrompt)) !== null) {
          const fullMatch = match[0];
          const index = parseInt(match[1]);
          const property = match[2];

          if (value[index] && value[index][property] !== undefined) {
            // 替换为对应的值
            tempPrompt = tempPrompt.replace(fullMatch, String(value[index][property]));
          }
        }

        generatedPrompt = tempPrompt;
      } else if (typeof value === 'object' && value !== null) {
        // 处理对象类型的参数
        for (const subKey in value) {
          const subValue = value[subKey];
          const objectPropertyPlaceholder = new RegExp(`\\{\\{${key}\\.${subKey}\\}\\}`, 'g');
          generatedPrompt = generatedPrompt.replace(objectPropertyPlaceholder, String(subValue));
        }

        // 同时支持直接使用对象名称作为占位符，将整个对象转为JSON字符串
        const objectPlaceholder = new RegExp(`\\{\\{${key}\\}\\}`, 'g');
        generatedPrompt = generatedPrompt.replace(objectPlaceholder, JSON.stringify(value, null, 2));
      } else if (value !== null && value !== undefined) {
        // 处理基本类型的参数
        const placeholder = new RegExp(`\\{\\{${key}\\}\\}`, 'g');
        generatedPrompt = generatedPrompt.replace(placeholder, String(value));
      }
    }

    return {
      prompt: generatedPrompt,
      template: promptTemplate.name,
      role: promptTemplate.role,
      parameters: promptTemplate.parameters
    };

  } catch (error) {
    console.error('生成提示词失败:', error);
    throw error;
  }
}

module.exports = exports;

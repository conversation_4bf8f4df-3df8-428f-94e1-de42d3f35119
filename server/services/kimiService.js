const axios = require('axios');

class KimiService {
  constructor(apiKey, baseUrl = 'https://api.moonshot.cn/v1') {
    this.apiKey = apiKey;
    // 确保 baseUrl 有效
    this.baseUrl = baseUrl || 'https://api.moonshot.cn/v1';

    console.log('KimiService 初始化:', {
      apiKey: apiKey ? apiKey.substring(0, 10) + '...' : 'null',
      baseUrl: this.baseUrl
    });

    this.client = axios.create({
      baseURL: this.baseUrl,
      headers: {
        'Authorization': `Bearer ${this.apiKey}`,
        'Content-Type': 'application/json'
      },
      timeout: 60000 // 60秒超时
    });
  }

  /**
   * 调用Kimi聊天完成API
   * @param {Object} options - 请求选项
   * @param {string} options.model - 模型名称，默认为 'moonshot-v1-8k'
   * @param {Array} options.messages - 消息数组
   * @param {number} options.max_tokens - 最大token数
   * @param {number} options.temperature - 温度参数
   * @param {boolean} options.stream - 是否流式输出
   * @returns {Promise<Object>} API响应
   */
  async chatCompletion(options = {}) {
    const {
      model = 'moonshot-v1-8k',
      messages,
      max_tokens = 4000,
      temperature = 0.7,
      stream = false,
      ...otherOptions
    } = options;

    if (!messages || !Array.isArray(messages) || messages.length === 0) {
      throw new Error('messages参数是必需的，且必须是非空数组');
    }

    try {
      const requestData = {
        model,
        messages,
        max_tokens,
        temperature,
        stream,
        ...otherOptions
      };

      console.log('发送Kimi API请求:', {
        model,
        messagesCount: messages.length,
        max_tokens,
        temperature
      });

      const response = await this.client.post('/chat/completions', requestData);
      
      console.log('Kimi API响应状态:', response.status);
      
      return response.data;
    } catch (error) {
      console.error('Kimi API调用失败:', error.response?.data || error.message);
      
      if (error.response) {
        // API返回了错误响应
        const { status, data } = error.response;
        throw new Error(`Kimi API错误 (${status}): ${data.error?.message || data.message || '未知错误'}`);
      } else if (error.request) {
        // 请求发送失败
        throw new Error('无法连接到Kimi API服务');
      } else {
        // 其他错误
        throw new Error(`Kimi API调用失败: ${error.message}`);
      }
    }
  }

  /**
   * 生成文本内容
   * @param {string} prompt - 提示词
   * @param {Object} options - 生成选项
   * @returns {Promise<string>} 生成的文本内容
   */
  async generateText(prompt, options = {}) {
    const messages = [
      {
        role: 'user',
        content: prompt
      }
    ];

    const response = await this.chatCompletion({
      messages,
      ...options
    });

    if (response.choices && response.choices.length > 0) {
      return response.choices[0].message.content;
    } else {
      throw new Error('Kimi API返回了空的响应');
    }
  }

  /**
   * 基于系统提示词和用户输入生成内容
   * @param {string} systemPrompt - 系统提示词
   * @param {string} userPrompt - 用户提示词
   * @param {Object} options - 生成选项
   * @returns {Promise<string>} 生成的文本内容
   */
  async generateWithSystemPrompt(systemPrompt, userPrompt, options = {}) {
    const messages = [
      {
        role: 'system',
        content: systemPrompt
      },
      {
        role: 'user',
        content: userPrompt
      }
    ];

    const response = await this.chatCompletion({
      messages,
      ...options
    });

    if (response.choices && response.choices.length > 0) {
      return response.choices[0].message.content;
    } else {
      throw new Error('Kimi API返回了空的响应');
    }
  }

  /**
   * 验证API密钥是否有效
   * @returns {Promise<boolean>} 是否有效
   */
  async validateApiKey() {
    try {
      const response = await this.generateText('测试', {
        max_tokens: 10,
        temperature: 0.1
      });
      return !!response;
    } catch (error) {
      console.error('Kimi API密钥验证失败:', error.message);
      return false;
    }
  }

  /**
   * 流式生成文本内容
   * @param {string} prompt - 提示词
   * @param {Object} options - 生成选项
   * @param {Function} onChunk - 接收数据块的回调函数
   * @returns {Promise<string>} 完整的生成内容
   */
  async generateTextStream(prompt, options = {}, onChunk) {
    const messages = [
      {
        role: 'user',
        content: prompt
      }
    ];

    return this.chatCompletionStream(messages, options, onChunk);
  }

  /**
   * 基于系统提示词流式生成内容
   * @param {string} systemPrompt - 系统提示词
   * @param {string} userPrompt - 用户提示词
   * @param {Object} options - 生成选项
   * @param {Function} onChunk - 接收数据块的回调函数
   * @returns {Promise<string>} 完整的生成内容
   */
  async generateWithSystemPromptStream(systemPrompt, userPrompt, options = {}, onChunk) {
    const messages = [
      {
        role: 'system',
        content: systemPrompt
      },
      {
        role: 'user',
        content: userPrompt
      }
    ];

    return this.chatCompletionStream(messages, options, onChunk);
  }

  /**
   * 流式聊天完成
   * @param {Array} messages - 消息数组
   * @param {Object} options - 生成选项
   * @param {Function} onChunk - 接收数据块的回调函数
   * @returns {Promise<string>} 完整的生成内容
   */
  async chatCompletionStream(messages, options = {}, onChunk) {
    const {
      model = 'moonshot-v1-8k',
      max_tokens = 4000,
      temperature = 0.7,
      ...otherOptions
    } = options;

    try {
      const requestData = {
        model,
        messages,
        max_tokens,
        temperature,
        stream: true, // 启用流式输出
        ...otherOptions
      };

      console.log('发送Kimi流式API请求:', {
        model,
        messagesCount: messages.length,
        max_tokens,
        temperature
      });

      const response = await this.client.post('/chat/completions', requestData, {
        responseType: 'stream'
      });

      let fullContent = '';

      return new Promise((resolve, reject) => {
        response.data.on('data', (chunk) => {
          const lines = chunk.toString().split('\n');
          
          for (const line of lines) {
            if (line.startsWith('data: ')) {
              const data = line.slice(6).trim();
              
              if (data === '[DONE]') {
                resolve(fullContent);
                return;
              }
              
              try {
                const parsed = JSON.parse(data);
                if (parsed.choices && parsed.choices[0] && parsed.choices[0].delta) {
                  const content = parsed.choices[0].delta.content;
                  if (content) {
                    fullContent += content;
                    if (onChunk) {
                      onChunk(content, fullContent);
                    }
                  }
                }
              } catch (parseError) {
                console.warn('解析Kimi流式响应失败:', parseError.message);
              }
            }
          }
        });

        response.data.on('end', () => {
          resolve(fullContent);
        });

        response.data.on('error', (error) => {
          console.error('Kimi流式API错误:', error);
          reject(new Error(`Kimi流式API调用失败: ${error.message}`));
        });
      });
    } catch (error) {
      console.error('Kimi流式API调用失败:', error.response?.data || error.message);
      console.error('完整错误信息:', error);

      if (error.response) {
        const { status, data } = error.response;
        console.error('API响应状态:', status);
        console.error('API响应数据:', data);

        let errorMessage = '未知错误';
        if (data) {
          if (typeof data === 'string') {
            errorMessage = data;
          } else if (data.error) {
            errorMessage = data.error.message || data.error.type || JSON.stringify(data.error);
          } else if (data.message) {
            errorMessage = data.message;
          } else {
            errorMessage = JSON.stringify(data);
          }
        }

        throw new Error(`Kimi API错误 (${status}): ${errorMessage}`);
      } else if (error.request) {
        throw new Error('无法连接到Kimi API服务');
      } else {
        throw new Error(`Kimi API调用失败: ${error.message}`);
      }
    }
  }
}

module.exports = KimiService;
